/**
 * Environment configuration for the application
 * This file centralizes all environment-specific configuration
 */

// API configuration
export const API_CONFIG = {
  // Base URL for API requests
  baseUrl: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',

  // Timeout for API requests in milliseconds (increased to 60 seconds)
  timeout: 60000,

  // Whether to include credentials in API requests
  withCredentials: false,

  // Retry configuration
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
};

// Application configuration
export const APP_CONFIG = {
  // Application name
  appName: 'GyaanRaksha Samyog Portal',

  // Application version
  version: '1.0.0',

  // Environment (development, staging, production)
  environment: import.meta.env.MODE || 'development',

  // Whether the application is running in production
  isProduction: import.meta.env.PROD || false,

  // Whether the application is running in development
  isDevelopment: import.meta.env.DEV || true,

  // Mock data is strictly prohibited across the entire application
  // This flag is kept for backward compatibility but is always set to false
  useMockData: false,
};

// Feature flags
export const FEATURES = {
  // Enable/disable features based on environment
  enableAnalytics: import.meta.env.PROD || false,
  enableErrorReporting: import.meta.env.PROD || false,
};

// Export default configuration
export default {
  api: API_CONFIG,
  app: APP_CONFIG,
  features: FEATURES,
};
