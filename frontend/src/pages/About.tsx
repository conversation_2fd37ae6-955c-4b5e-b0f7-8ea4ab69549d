import React, { useEffect } from 'react';
import Header from '@/components/Header';
import RRUHeader from '@/components/RRUHeader';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';

const About = () => {
  // Add scroll animation effect
  useEffect(() => {
    const handleScroll = () => {
      const elements = document.querySelectorAll('.animate-on-scroll');
      elements.forEach((element) => {
        const rect = element.getBoundingClientRect();
        const isVisible = rect.top < window.innerHeight - 100;
        if (isVisible) {
          element.classList.add('animate-visible');
        }
      });
    };

    // Initial check
    handleScroll();

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    // Clean up
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <RRUHeader />
      <Header />

      <main className="flex-grow">
        {/* Hero Section */}
        <section className="py-16 bg-white relative">
  <div className="container mx-auto px-4">
    <div className="max-w-4xl mx-auto relative">
      {/* "About" Label */}
      <div className="absolute -top-5 left-1/2 transform -translate-x-1/2 bg-white px-4 text-gyaan-black font-semibold text-lg z-10">
        ABOUT
      </div>

      {/* Box wrapper */}
      <div className="border-2 border-gyaan-navy rounded-xl shadow-md p-8 text-center">
        <h1 className="text-4xl md:text-5xl font-bold text-gyaan-navy mb-6">
          Rashtriya Raksha University
        </h1>
        <div className="w-24 h-1 bg-gyaan-gold mx-auto mb-6"></div>
        <p className="text-xl text-gray-600 leading-relaxed">
          A premier national security and police university dedicated to
          creating a safe, secure, and peaceful environment through
          professional education, training, and research.
        </p>
      </div>
    </div>
  </div>
</section>


        {/* University Overview */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-gyaan-navy mb-4">
                  Our Legacy
                </h2>
                <div className="w-24 h-1 bg-gyaan-gold mx-auto"></div>
              </div>

              <div className="space-y-6">
                <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed text-lg">
                    <span className="font-semibold text-gyaan-navy">
                      Rashtriya Raksha University (RRU)
                    </span>
                    , formerly known as Raksha Shakti University, was
                    established in 2009 by the Government of Gujarat. In 2020,
                    the Parliament of India passed the Rashtriya Raksha
                    University Act, elevating it to an{' '}
                    <span className="font-semibold text-gyaan-navy">
                      Institution of National Importance
                    </span>
                    .
                  </p>
                </div>

                <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed text-lg">
                    As India's first national security and police university,
                    RRU is dedicated to meeting the nation's need for
                    professional, trained manpower in{' '}
                    <span className="font-semibold text-gyaan-navy">
                      policing, criminal justice, security, defense, and related
                      fields
                    </span>
                    .
                  </p>
                </div>

                <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed text-lg">
                    The university operates under the{' '}
                    <span className="font-semibold text-gyaan-navy">
                      Ministry of Home Affairs, Government of India
                    </span>
                    , and serves as a catalyst for promoting academic study and
                    research in national defense and security.
                  </p>
                </div>

                <div className="text-center mt-10">
                  <Button className="bg-gyaan-navy hover:bg-gyaan-navy/90 text-white px-8 py-3 text-lg">
                    Learn More About RRU
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Vision and Mission */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-5xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-gyaan-navy mb-4">
                  Vision & Mission
                </h2>
                <div className="w-24 h-1 bg-gyaan-gold mx-auto"></div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Vision Card */}
                <div className="bg-white rounded-lg overflow-hidden shadow-lg border border-gray-200">
                  <div className="bg-gyaan-navy p-6">
                    <h3 className="text-xl font-bold text-white flex items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6 mr-3"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        />
                      </svg>
                      Our Vision
                    </h3>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-700 leading-relaxed text-lg">
                      To be a{' '}
                      <span className="font-semibold text-gyaan-navy">
                        world-class university
                      </span>{' '}
                      for national security and police education, creating a
                      safe, secure, and peaceful environment through
                      professional education, training, and research.
                    </p>
                  </div>
                </div>

                {/* Mission Card */}
                <div className="bg-white rounded-lg overflow-hidden shadow-lg border border-gray-200">
                  <div className="bg-gyaan-navy p-6">
                    <h3 className="text-xl font-bold text-white flex items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6 mr-3"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                        />
                      </svg>
                      Our Mission
                    </h3>
                  </div>
                  <div className="p-6">
                    <ul className="space-y-3">
                      <li className="flex items-start">
                        <span className="text-gyaan-gold mr-3 mt-1 text-lg">
                          •
                        </span>
                        <span className="text-gray-700">
                          Provide quality education and training in national
                          security and policing
                        </span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-gyaan-gold mr-3 mt-1 text-lg">
                          •
                        </span>
                        <span className="text-gray-700">
                          Conduct research and development in security, defense,
                          and strategic areas
                        </span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-gyaan-gold mr-3 mt-1 text-lg">
                          •
                        </span>
                        <span className="text-gray-700">
                          Create a pool of trained professionals for security
                          and police organizations
                        </span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-gyaan-gold mr-3 mt-1 text-lg">
                          •
                        </span>
                        <span className="text-gray-700">
                          Promote collaboration between academia, industry, and
                          security agencies
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Academic Programs */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-gyaan-navy mb-4">
                  Academic Programs
                </h2>
                <div className="w-24 h-1 bg-gyaan-gold mx-auto mb-6"></div>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  RRU offers a wide range of academic programs designed to
                  prepare students for careers in national security, policing,
                  and related fields.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {/* School 1 */}
                <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-bold text-gyaan-navy mb-4 flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-3 text-gyaan-navy"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                      />
                    </svg>
                    School of Police Science & Security
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Offers programs in police administration, forensic science,
                    and security management.
                  </p>
                  <Button className="w-full bg-gyaan-navy hover:bg-gyaan-navy/90 text-white">
                    Explore Programs
                  </Button>
                </div>

                {/* School 2 */}
                <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-bold text-gyaan-navy mb-4 flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-3 text-gyaan-navy"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                      />
                    </svg>
                    School of Internal Security & Strategic Studies
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Focuses on internal security, counter-terrorism, and
                    strategic studies.
                  </p>
                  <Button className="w-full bg-gyaan-navy hover:bg-gyaan-navy/90 text-white">
                    Explore Programs
                  </Button>
                </div>

                {/* School 3 */}
                <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-bold text-gyaan-navy mb-4 flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-3 text-gyaan-navy"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                    School of Information Technology & Cybersecurity
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Specializes in cybersecurity, digital forensics, and
                    information security.
                  </p>
                  <Button className="w-full bg-gyaan-navy hover:bg-gyaan-navy/90 text-white">
                    Explore Programs
                  </Button>
                </div>

                {/* School 4 */}
                <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-bold text-gyaan-navy mb-4 flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-3 text-gyaan-navy"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2"
                      />
                    </svg>
                    School of Criminology & Behavioral Sciences
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Offers programs in criminology, criminal psychology, and
                    behavioral analysis.
                  </p>
                  <Button className="w-full bg-gyaan-navy hover:bg-gyaan-navy/90 text-white">
                    Explore Programs
                  </Button>
                </div>

                {/* School 5 */}
                <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-bold text-gyaan-navy mb-4 flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-3 text-gyaan-navy"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                      />
                    </svg>
                    School of Management Studies
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Focuses on security management, disaster management, and
                    crisis response.
                  </p>
                  <Button className="w-full bg-gyaan-navy hover:bg-gyaan-navy/90 text-white">
                    Explore Programs
                  </Button>
                </div>

                {/* School 6 */}
                <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-bold text-gyaan-navy mb-4 flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-3 text-gyaan-navy"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"
                      />
                    </svg>
                    School of Law, Justice & Governance
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Offers programs in security law, criminal justice, and
                    governance.
                  </p>
                  <Button className="w-full bg-gyaan-navy hover:bg-gyaan-navy/90 text-white">
                    Explore Programs
                  </Button>
                </div>
              </div>

              <div className="text-center mt-12">
                <Button className="bg-gyaan-navy hover:bg-gyaan-navy/90 text-white px-8 py-3 text-lg">
                  View All Academic Programs
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default About;
