import axios, { AxiosError, AxiosResponse } from 'axios';
import { toast } from '@/components/ui/use-toast';
import { API_CONFIG, APP_CONFIG } from '@/config/environment';

// Create axios instance with environment configuration
const api = axios.create({
  baseURL: API_CONFIG.baseUrl,
  timeout: API_CONFIG.timeout,
  withCredentials: API_CONFIG.withCredentials,
  headers: {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    Pragma: 'no-cache',
    Expires: '0',
  },
});

// Log configuration in development mode
if (APP_CONFIG.isDevelopment) {
  console.log('API Configuration:', {
    baseURL: API_CONFIG.baseUrl,
    environment: APP_CONFIG.environment,
  });
}

// Add request interceptor for logging and other pre-request operations
api.interceptors.request.use(
  (config) => {
    // Add a cache buster to GET requests to prevent OPTIONS requests
    if (config.method === 'get') {
      // Add timestamp to URL to prevent caching
      config.params = {
        ...config.params,
        _t: new Date().getTime(),
      };
    }

    // Enhanced logging for requests
    if (APP_CONFIG.isDevelopment) {
      console.log(
        `API Request [${config.method?.toUpperCase()}] ${config.url}`,
        {
          headers: config.headers,
          params: config.params,
          data: config.data,
        }
      );
    }

    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Token management methods
export const setAuthToken = (token: string) => {
  if (token) {
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }
};

export const clearAuthToken = () => {
  delete api.defaults.headers.common['Authorization'];
};

// Add request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Retry function for failed requests
const retryRequest = async (
  error: AxiosError,
  retryCount = 0
): Promise<any> => {
  const maxRetries = API_CONFIG.retryAttempts || 3;
  const retryDelay = API_CONFIG.retryDelay || 1000;

  if (retryCount >= maxRetries) {
    return Promise.reject(error);
  }

  // Only retry on timeout or network errors
  if (
    error.code === 'ECONNABORTED' ||
    error.code === 'NETWORK_ERROR' ||
    !error.response
  ) {
    console.log(
      `Retrying request (attempt ${retryCount + 1}/${maxRetries}) for ${
        error.config?.url
      }`
    );

    // Wait before retrying
    await new Promise((resolve) =>
      setTimeout(resolve, retryDelay * (retryCount + 1))
    );

    try {
      return await api.request(error.config!);
    } catch (retryError) {
      return retryRequest(retryError as AxiosError, retryCount + 1);
    }
  }

  return Promise.reject(error);
};

// Add response interceptor to handle errors
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // Enhanced logging for successful responses
    if (APP_CONFIG.isDevelopment) {
      console.log(
        `API Response Success [${
          response.status
        }] ${response.config.method?.toUpperCase()} ${response.config.url}`,
        {
          data: response.data,
          headers: response.headers,
        }
      );
    }
    return response;
  },
  async (error: AxiosError) => {
    // Enhanced error logging for debugging
    console.error('API Error:', {
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      code: error.code,
      stack: error.stack,
    });

    // Try to retry the request for timeout and network errors
    if (error.code === 'ECONNABORTED' || !error.response) {
      try {
        return await retryRequest(error);
      } catch (retryError) {
        // If retry fails, continue with normal error handling
        error = retryError as AxiosError;
      }
    }

    // Handle authentication errors
    if (error.response?.status === 401) {
      // Clear token if it's an auth error
      const errorMessage = error.response?.data?.message || '';
      if (
        errorMessage.includes('Not authorized') ||
        errorMessage.includes('Invalid token')
      ) {
        console.warn('Authentication error detected, clearing token');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        clearAuthToken();

        // Redirect to login if not already there
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
      }
    }

    // Get error details
    const status = error.response?.status;
    const message =
      error.response?.data?.message ||
      error.response?.data?.error ||
      error.message ||
      'An unexpected error occurred';

    // Handle different error statuses
    switch (status) {
      case 401:
        // Unauthorized - clear token and redirect to login
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/login';
        toast({
          title: 'Session Expired',
          description: 'Please log in again to continue.',
          variant: 'destructive',
        });
        break;

      case 403:
        // Forbidden - user doesn't have permission
        toast({
          title: 'Access Denied',
          description: 'You do not have permission to perform this action.',
          variant: 'destructive',
        });
        break;

      case 404:
        // Not found - but don't show toast for dashboard endpoints
        const url = error.config?.url || '';
        const isDashboardEndpoint =
          url.includes('/admin/dashboard') ||
          url.includes('/trends/') ||
          url.includes('/stats');

        // Only show toast for non-dashboard endpoints
        if (!isDashboardEndpoint) {
          toast({
            title: 'Not Found',
            description: 'The requested resource was not found.',
            variant: 'destructive',
          });
        } else {
          console.warn('Dashboard endpoint not found:', url);
        }
        break;

      case 422:
        // Validation error
        toast({
          title: 'Validation Error',
          description: message,
          variant: 'destructive',
        });
        break;

      case 500:
        // Server error
        toast({
          title: 'Server Error',
          description:
            'Something went wrong on our end. Please try again later.',
          variant: 'destructive',
        });
        break;

      default:
        // Timeout errors
        if (error.code === 'ECONNABORTED') {
          toast({
            title: 'Request Timeout',
            description:
              'The request took too long to complete after multiple attempts. Please try again later.',
            variant: 'destructive',
          });
        }
        // Network errors
        else if (error.message === 'Network Error' || !navigator.onLine) {
          toast({
            title: 'Network Error',
            description: 'Please check your internet connection and try again.',
            variant: 'destructive',
          });
        }
        // API errors with status codes
        else if (status && status >= 400) {
          // Don't show toast for dashboard 404 errors
          const url = error.config?.url || '';
          const isDashboard404 =
            status === 404 &&
            (url.includes('/admin/dashboard') ||
              url.includes('/trends/') ||
              url.includes('/stats'));

          if (!isDashboard404) {
            toast({
              title: 'Error',
              description: message,
              variant: 'destructive',
            });
          } else {
            console.warn('Dashboard endpoint not found:', url);
          }
        }
        // Unknown errors
        else {
          toast({
            title: 'Unexpected Error',
            description:
              'An unexpected error occurred. Please try again later.',
            variant: 'destructive',
          });
        }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (userData: any) => api.post('/auth/register', userData),
  login: (credentials: any) => {
    // Handle admin login differently if needed
    if (credentials.isAdmin) {
      return api.post('/auth/admin/login', {
        email: credentials.email,
        password: credentials.password,
      });
    }
    return api.post('/auth/login', credentials);
  },
  verifyEmail: (token: string) => api.get(`/auth/verify-email/${token}`),
  verifyMobile: (data: any) => api.post('/auth/verify-mobile', data),
  resendEmailVerification: (data: { email: string }) =>
    api.post('/auth/resend-email-verification', data),
  forgotPassword: (email: string) =>
    api.post('/auth/forgot-password', { email }),
  resetPassword: (token: string, password: string) =>
    api.put(`/auth/reset-password/${token}`, { password }),
  updatePassword: (data: any) => api.put('/auth/update-password', data),
  getCurrentUser: () => api.get('/auth/me?_t=' + new Date().getTime()),
  logout: () => api.get('/auth/logout'),

  // Refresh token - this will validate the current token
  refreshToken: async () => {
    try {
      const response = await api.get('/auth/me?_t=' + new Date().getTime());
      return response.data;
    } catch (error) {
      // If token is invalid, clear it
      if (error.response?.status === 401) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        clearAuthToken();
      }
      throw error;
    }
  },

  // Token management
  setAuthToken,
  clearAuthToken,
};

// User Management API (Admin only) - Moved up to fix reference order
export const userManagementAPI = {
  getAllUsers: (params = {}) => {
    console.log('Calling getAllUsers API with params:', params);
    return api.get('/admin/users', { params });
  },
  getUserDetails: (id: string) => {
    console.log('Calling getUserDetails API for id:', id);
    return api.get(`/admin/users/${id}`);
  },
  createUser: (data: any) => {
    console.log('Calling createUser API with data:', data);
    return api.post('/admin/users', data);
  },
  updateUser: (id: string, data: any) => {
    console.log('Calling updateUser API for id:', id, 'with data:', data);
    return api.put(`/admin/users/${id}`, data);
  },
  deleteUser: (id: string) => {
    console.log('Calling deleteUser API for id:', id);
    return api.delete(`/admin/users/${id}`);
  },
  changeUserRole: (id: string, data: any) => {
    console.log('Calling changeUserRole API for id:', id, 'with data:', data);
    return api.put(`/admin/users/${id}/role`, data);
  },
};

// User API
export const userAPI = {
  // Candidate endpoints
  updateProfile: (data: any) => api.put('/users/profile', data),
  getNotifications: (page = 1, limit = 10) =>
    api.get(`/users/notifications?page=${page}&limit=${limit}`),
  markNotificationRead: (id: string) => api.put(`/users/notifications/${id}`),
  markAllNotificationsRead: () => api.put('/users/notifications'),

  // Admin endpoints - using userManagementAPI
  getUsers: userManagementAPI.getAllUsers,
  getUser: userManagementAPI.getUserDetails,
  createUser: userManagementAPI.createUser,
  updateUser: userManagementAPI.updateUser,
  deleteUser: userManagementAPI.deleteUser,
  exportUsers: () => api.get('/admin/users/export', { responseType: 'blob' }),
  resetUserPassword: (id: string) =>
    api.post(`/admin/users/${id}/reset-password`),
  getUserActivity: (id: string) => api.get(`/admin/users/${id}/activity`),
};

// Application API
export const applicationAPI = {
  // Candidate endpoints
  createApplication: (data?: any) => api.post('/applications', data || {}),
  getMyApplications: () => api.get('/applications'),
  getApplication: (id: string) => api.get(`/applications/${id}`),
  updateApplication: (id: string, data: any) =>
    api.put(`/applications/${id}`, data),
  submitApplication: (id: string) => api.put(`/applications/${id}/submit`),
  deleteApplication: (id: string) => api.delete(`/applications/${id}`),

  // Admin endpoints
  getAllApplications: (params = {}) =>
    api.get('/admin/applications', { params }),
  getApplicationDetails: (id: string) => api.get(`/admin/applications/${id}`),
  approveApplication: (id: string, data?: any) =>
    api.put(`/admin/applications/${id}/approve`, data || {}),
  rejectApplication: (id: string, data: any) =>
    api.put(`/admin/applications/${id}/reject`, data),
  getApplicationStats: () => api.get('/admin/applications/stats'),
};

// Course API
export const courseAPI = {
  // Public and candidate endpoints
  getCourses: (params = {}) => {
    console.log('Calling getCourses API with params:', params);
    // Try both endpoints - first the admin endpoint, then fall back to the public one
    return api.get('/admin/courses', { params }).catch((error) => {
      console.log(
        'Admin courses endpoint failed, trying public endpoint:',
        error
      );
      return api.get('/courses', { params });
    });
  },
  getCourse: (id: string) => {
    console.log('Calling getCourse API for id:', id);
    return api.get(`/courses/${id}`);
  },
  getCourseBatches: (id: string) => {
    console.log('Calling getCourseBatches API for id:', id);
    return api.get(`/courses/${id}/batches`);
  },

  // Admin endpoints
  createCourse: (data: any) => {
    console.log('Calling createCourse API with data:', data);
    return api.post('/admin/courses', data);
  },
  updateCourse: (id: string, data: any) => {
    console.log('Calling updateCourse API for id:', id, 'with data:', data);
    return api.put(`/admin/courses/${id}`, data);
  },
  deleteCourse: (id: string) => {
    console.log('Calling deleteCourse API for id:', id);
    return api.delete(`/admin/courses/${id}`);
  },
  getCourseEnrollments: (id: string, params = {}) => {
    console.log(
      'Calling getCourseEnrollments API for id:',
      id,
      'with params:',
      params
    );
    return api.get(`/admin/courses/${id}/enrollments`, { params });
  },
  getCourseStats: () => {
    console.log('Calling getCourseStats API');
    return api.get('/admin/courses/stats');
  },
  exportCourses: () => {
    console.log('Calling exportCourses API');
    return api.get('/admin/courses/export', { responseType: 'blob' });
  },
};

// Batch API
export const batchAPI = {
  // Public and candidate endpoints
  getBatches: (params = {}) => api.get('/batches', { params }),
  getBatch: (id: string) => api.get(`/batches/${id}`),

  // Admin endpoints
  createBatch: (data: any) => api.post('/admin/batches', data),
  updateBatch: (id: string, data: any) => api.put(`/admin/batches/${id}`, data),
  deleteBatch: (id: string) => api.delete(`/admin/batches/${id}`),
  getBatchEnrollments: (id: string, params = {}) =>
    api.get(`/admin/batches/${id}/enrollments`, { params }),
  exportBatches: () =>
    api.get('/admin/batches/export', { responseType: 'blob' }),
};

// Counseling API
export const counselingAPI = {
  // Candidate endpoints
  getMySessions: (params = {}) => api.get('/counseling/sessions', { params }),
  getSession: (id: string) => api.get(`/counseling/sessions/${id}`),
  submitFeedback: (id: string, data: any) =>
    api.post(`/counseling/sessions/${id}/feedback`, data),

  // Admin endpoints
  getAllSessions: (params = {}) =>
    api.get('/admin/counseling/sessions', { params }),
  createSession: (data: any) => api.post('/admin/counseling/sessions', data),
  updateSession: (id: string, data: any) =>
    api.put(`/admin/counseling/sessions/${id}`, data),
  completeSession: (id: string) =>
    api.put(`/admin/counseling/sessions/${id}/complete`),
  cancelSession: (id: string, data: any) =>
    api.put(`/admin/counseling/sessions/${id}/cancel`, data),
};

// Helpdesk API
export const helpdeskAPI = {
  // Candidate endpoints
  getMyTickets: (params = {}) => {
    console.log('Calling getMyTickets API with params:', params);
    return api.get('/helpdesk/tickets', { params });
  },
  getTicket: (id: string) => {
    console.log('Calling getTicket API for id:', id);
    return api.get(`/helpdesk/tickets/${id}`);
  },
  createTicket: (data: any) => {
    console.log('Calling createTicket API with data:', data);
    return api.post('/helpdesk/tickets', data);
  },
  getTicketResponses: (ticketId: string) => {
    console.log('Calling getTicketResponses API for ticketId:', ticketId);
    return api.get(`/helpdesk/tickets/${ticketId}/responses`);
  },
  addResponse: (ticketId: string, data: any) => {
    console.log(
      'Calling addResponse API for ticketId:',
      ticketId,
      'with data:',
      data
    );
    return api.post(`/helpdesk/tickets/${ticketId}/responses`, data);
  },
  downloadAttachment: (attachmentId: string) => {
    console.log(
      'Calling downloadAttachment API for attachmentId:',
      attachmentId
    );
    return api.get(`/helpdesk/attachments/${attachmentId}`, {
      responseType: 'blob',
    });
  },

  // Admin endpoints
  getAllTickets: (params = {}) => {
    console.log('Calling getAllTickets API with params:', params);
    // Try both endpoints - first the admin endpoint, then fall back to the candidate one
    return api
      .get('/admin/helpdesk/tickets', { params })
      .catch((error) => {
        console.log(
          'Admin helpdesk endpoint failed, trying support endpoint:',
          error
        );
        return api.get('/support/tickets', { params });
      })
      .catch((error) => {
        console.log(
          'Support endpoint failed, trying helpdesk endpoint:',
          error
        );
        return api.get('/helpdesk/tickets', { params });
      });
  },
  updateTicketStatus: (ticketId: string, data: any) => {
    console.log(
      'Calling updateTicketStatus API for ticketId:',
      ticketId,
      'with data:',
      data
    );
    return api.put(`/admin/helpdesk/tickets/${ticketId}/status`, data);
  },
  assignTicket: (ticketId: string, data: any) => {
    console.log(
      'Calling assignTicket API for ticketId:',
      ticketId,
      'with data:',
      data
    );
    return api.put(`/admin/helpdesk/tickets/${ticketId}/assign`, data);
  },
};

// Academic Progression API
export const progressionAPI = {
  // Candidate endpoints
  getMyProgress: (params = {}) => api.get('/progression/courses', { params }),
  getModuleDetails: (moduleId: string) =>
    api.get(`/progression/modules/${moduleId}`),
  getAssessmentDetails: (assessmentId: string) =>
    api.get(`/progression/assessments/${assessmentId}`),
  submitAssessment: (assessmentId: string, data: any) =>
    api.post(`/progression/assessments/${assessmentId}/submit`, data),
  downloadCertificate: (courseId: string) =>
    api.get(`/progression/certificates/${courseId}`, { responseType: 'blob' }),

  // Admin endpoints
  getAllStudentsProgress: (params = {}) =>
    api.get('/admin/progression/students', { params }),
  getStudentProgress: (studentId: string, params = {}) =>
    api.get(`/admin/progression/students/${studentId}`, { params }),
  getCourses: (params = {}) => api.get('/admin/courses', { params }),
  generateCertificate: (studentId: string, courseId: string) =>
    api.post(
      `/admin/progression/students/${studentId}/courses/${courseId}/certificate`
    ),
  exportProgressReport: (studentId: string) =>
    api.get(`/admin/progression/students/${studentId}/report`, {
      responseType: 'blob',
    }),
};

// Notifications API
export const notificationAPI = {
  // Candidate endpoints
  getNotifications: (params = {}) => api.get('/notifications', { params }),
  markNotificationRead: (id: string) => api.put(`/notifications/${id}`),
  markAllNotificationsRead: () => api.put('/notifications'),
  getNotificationSettings: () => api.get('/notifications/settings'),
  updateNotificationSettings: (data: any) =>
    api.put('/notifications/settings', data),

  // Admin endpoints
  sendNotification: (data: any) => api.post('/admin/notifications', data),
  sendBulkNotifications: (data: any) =>
    api.post('/admin/notifications/bulk', data),
};

// Reports API
export const reportsAPI = {
  // Admin endpoints
  getDashboardAnalytics: (params = {}) =>
    api.get('/admin/reports/dashboard', { params }),
  getEnrollmentAnalytics: (params = {}) =>
    api.get('/admin/reports/enrollment', { params }),
  getCourseAnalytics: (params = {}) =>
    api.get('/admin/reports/courses', { params }),
  getRevenueAnalytics: (params = {}) =>
    api.get('/admin/reports/revenue', { params }),
  getUserAnalytics: (params = {}) =>
    api.get('/admin/reports/users', { params }),
  exportReport: (reportType: string, params = {}) =>
    api.get(`/admin/reports/${reportType}/export`, {
      params,
      responseType: 'blob',
    }),
};

// Document API
export const documentAPI = {
  // Candidate endpoints
  uploadDocument: (formData: FormData) => {
    // Log the form data for debugging
    if (APP_CONFIG.isDevelopment) {
      console.log('Uploading document with form data:', {
        documentType: formData.get('documentType'),
        type: formData.get('type'),
        file: formData.get('file') ? 'File present' : 'No file',
      });
    }

    return api.post('/documents', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Upload document with progress tracking
  uploadDocumentWithProgress: (
    formData: FormData,
    onUploadProgress: (progressEvent: any) => void
  ) => {
    return api.post('/documents', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: onUploadProgress,
      // Add timeout to prevent hanging requests
      timeout: 30000, // 30 seconds
    });
  },
  getMyDocuments: () => api.get('/documents'),
  getApplicationDocuments: (applicationId: string) =>
    api.get(`/documents/application/${applicationId}`),
  getDocument: (id: string) => api.get(`/documents/${id}`),
  downloadDocument: (id: string) =>
    api.get(`/documents/${id}/download`, { responseType: 'blob' }),
  deleteDocument: (id: string) => api.delete(`/documents/${id}`),

  // Admin endpoints
  verifyDocument: (id: string, data: any) =>
    api.put(`/admin/documents/${id}/verify`, data),
  rejectDocument: (id: string, data: any) =>
    api.put(`/admin/documents/${id}/reject`, data),
  getAllDocuments: (params = {}) => api.get('/admin/documents', { params }),
};

// Payment API
export const paymentAPI = {
  // Candidate endpoints
  getMyPayments: () => api.get('/payments'),
  initiatePayment: (applicationId: string, data: any) =>
    api.post(`/payments/initiate/${applicationId}`, data),
  verifyPayment: (paymentId: string, data: any) =>
    api.post(`/payments/verify/${paymentId}`, data),
  getPaymentReceipt: (paymentId: string) =>
    api.get(`/payments/${paymentId}/receipt`, { responseType: 'blob' }),

  // Admin endpoints
  getAllPayments: (params = {}) => api.get('/admin/payments', { params }),
  getPaymentDetails: (id: string) => api.get(`/admin/payments/${id}`),
  updatePaymentStatus: (id: string, data: any) =>
    api.put(`/admin/payments/${id}/status`, data),
  getPaymentStats: () => api.get('/admin/payments/stats'),
};

// Enrollment API
export const enrollmentAPI = {
  // Candidate endpoints
  getMyEnrollments: () => api.get('/enrollments'),
  getEnrollmentDetails: (id: string) => api.get(`/enrollments/${id}`),
  getCourseContent: (courseId: string) =>
    api.get(`/enrollments/courses/${courseId}/content`),
  getModuleContent: (courseId: string, moduleId: string) =>
    api.get(`/enrollments/courses/${courseId}/modules/${moduleId}`),
  getLessonContent: (courseId: string, lessonId: string) =>
    api.get(`/enrollments/courses/${courseId}/lessons/${lessonId}`),
  markLessonComplete: (lessonId: string) =>
    api.put(`/enrollments/lessons/${lessonId}/complete`),
  submitAssessment: (assessmentId: string, data: any) =>
    api.post(`/enrollments/assessments/${assessmentId}/submit`, data),
  downloadCertificate: (enrollmentId: string) =>
    api.get(`/enrollments/${enrollmentId}/certificate`, {
      responseType: 'blob',
    }),

  // Admin endpoints
  getAllEnrollments: (params = {}) => api.get('/admin/enrollments', { params }),
  createEnrollment: (data: any) => api.post('/admin/enrollments', data),
  updateEnrollment: (id: string, data: any) =>
    api.put(`/admin/enrollments/${id}`, data),
  deleteEnrollment: (id: string) => api.delete(`/admin/enrollments/${id}`),
};

// Support API
export const supportAPI = {
  // Candidate endpoints
  createTicket: (data: any) => api.post('/support/tickets', data),
  getMyTickets: (params = {}) => api.get('/support/tickets', { params }),
  getTicketDetails: (id: string) => api.get(`/support/tickets/${id}`),
  replyToTicket: (id: string, data: any) =>
    api.post(`/support/tickets/${id}/replies`, data),
  closeTicket: (id: string) => api.put(`/support/tickets/${id}/close`),

  // Admin endpoints
  getAllTickets: (params = {}) => api.get('/admin/support/tickets', { params }),
  assignTicket: (id: string, data: any) =>
    api.put(`/admin/support/tickets/${id}/assign`, data),
  updateTicketStatus: (id: string, data: any) =>
    api.put(`/admin/support/tickets/${id}/status`, data),
  getTicketStats: () => api.get('/admin/support/tickets/stats'),
};

// Bulk Upload API
export const bulkUploadAPI = {
  // Admin endpoints
  uploadFile: (formData: FormData, config?: any) =>
    api.post('/admin/bulk-upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    }),
  getUploadHistory: (params = {}) =>
    api.get('/admin/bulk-upload/history', { params }),
  downloadTemplate: (type: string) =>
    api.get(`/admin/bulk-upload/template/${type}`, {
      responseType: 'blob',
    }),
};

// Communication API
export const communicationAPI = {
  // Email template endpoints
  getEmailTemplates: () => api.get('/admin/communications/email-templates'),
  getEmailTemplate: (id: string) =>
    api.get(`/admin/communications/email-templates/${id}`),
  createEmailTemplate: (data: any) =>
    api.post('/admin/communications/email-templates', data),
  updateEmailTemplate: (id: string, data: any) =>
    api.put(`/admin/communications/email-templates/${id}`, data),
  deleteEmailTemplate: (id: string) =>
    api.delete(`/admin/communications/email-templates/${id}`),

  // SMS template endpoints
  getSmsTemplates: () => api.get('/admin/communications/sms-templates'),
  getSmsTemplate: (id: string) =>
    api.get(`/admin/communications/sms-templates/${id}`),
  createSmsTemplate: (data: any) =>
    api.post('/admin/communications/sms-templates', data),
  updateSmsTemplate: (id: string, data: any) =>
    api.put(`/admin/communications/sms-templates/${id}`, data),
  deleteSmsTemplate: (id: string) =>
    api.delete(`/admin/communications/sms-templates/${id}`),

  // Send communication endpoints
  sendEmail: (data: any) => api.post('/admin/communications/send-email', data),
  sendSms: (data: any) => api.post('/admin/communications/send-sms', data),
  sendBulkEmail: (data: any) =>
    api.post('/admin/communications/send-bulk-email', data),
  sendBulkSms: (data: any) =>
    api.post('/admin/communications/send-bulk-sms', data),
};

// Analytics API
export const analyticsAPI = {
  // Overview analytics
  getOverview: (timeRange: string = 'year') =>
    api.get('/admin/analytics/overview', { params: { timeRange } }),

  // Enrollment analytics
  getEnrollmentAnalytics: (params = {}) =>
    api.get('/admin/analytics/enrollment', { params }),

  // Revenue analytics
  getRevenueAnalytics: (params = {}) =>
    api.get('/admin/analytics/revenue', { params }),

  // Performance analytics
  getPerformanceAnalytics: (params = {}) =>
    api.get('/admin/analytics/performance', { params }),

  // Export analytics reports
  exportAnalyticsReport: (reportType: string, params = {}) =>
    api.get(`/admin/analytics/export/${reportType}`, {
      params,
      responseType: 'blob',
    }),
};

// Audit API
export const auditAPI = {
  // Admin endpoints
  getLogs: (params = {}) => api.get('/admin/audit/logs', { params }),
  getLogDetails: (id: string) => api.get(`/admin/audit/logs/${id}`),
  exportLogs: (params = {}) =>
    api.get('/admin/audit/logs/export', {
      params,
      responseType: 'blob',
    }),
};

// Dashboard API (Admin only)
export const dashboardAPI = {
  // No fallback data - let errors propagate to be handled by the components
  getStats: () => {
    return api.get('/admin/dashboard/stats', {
      timeout: 10000, // 10 seconds timeout
    });
  },

  getRecentActivity: () => {
    return api.get('/admin/dashboard/activity', {
      timeout: 10000,
    });
  },

  getEnrollmentTrends: (period = 'monthly') => {
    return api.get(`/admin/dashboard/trends/enrollments?period=${period}`, {
      timeout: 10000,
    });
  },

  getApplicationTrends: (period = 'monthly') => {
    return api.get(`/admin/dashboard/trends/applications?period=${period}`, {
      timeout: 10000,
    });
  },

  getRevenueTrends: (period = 'monthly') => {
    return api.get(`/admin/dashboard/trends/revenue?period=${period}`, {
      timeout: 10000,
    });
  },
};

// Stats API for public statistics with enhanced timeout handling
export const statsAPI = {
  getStats: () => {
    console.log('Fetching stats data...');
    return api.get('/stats', {
      timeout: 45000, // 45 seconds for stats
      headers: {
        'Cache-Control': 'no-cache',
        Pragma: 'no-cache',
      },
    });
  },
};

// Partners API for public partner information with enhanced timeout handling
export const partnersAPI = {
  getPartners: () => {
    console.log('Fetching partners data...');
    return api.get('/partners', {
      timeout: 45000, // 45 seconds for partners
      headers: {
        'Cache-Control': 'no-cache',
        Pragma: 'no-cache',
      },
    });
  },
  getPartner: (id: string) => api.get(`/partners/${id}`, { timeout: 30000 }),
};

// Courses API for public course information
export const coursesAPI = {
  getCourses: () => api.get('/courses'),
  getCourse: (id: string) => api.get(`/courses/${id}`),
};

// Export the API instance
export default api;
