import { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const closeMenu = () => setIsMenuOpen(false);

  return (
    <header className="bg-gyaan-navy shadow-md w-full z-50">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <NavLink to="/" className="flex items-center" onClick={closeMenu}>
              <span className="text-2xl font-bold text-white">GyaanRaksha</span>
              <span className="text-2xl font-bold text-gyaan-gold">Samyog</span>
            </NavLink>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <NavLink
              to="/"
              className={({ isActive }) =>
                `text-sm font-medium ${
                  isActive
                    ? 'text-gyaan-gold'
                    : 'text-white hover:text-gyaan-gold'
                }`
              }
            >
              Home
            </NavLink>
            <NavLink
              to="/about"
              className={({ isActive }) =>
                `text-sm font-medium ${
                  isActive
                    ? 'text-gyaan-gold'
                    : 'text-white hover:text-gyaan-gold'
                }`
              }
            >
              About RRU
            </NavLink>
            <NavLink
              to="/news"
              className={({ isActive }) =>
                `text-sm font-medium ${
                  isActive
                    ? 'text-gyaan-gold'
                    : 'text-white hover:text-gyaan-gold'
                }`
              }
            >
              News & Announcements
            </NavLink>
            <NavLink
              to="/contact"
              className={({ isActive }) =>
                `text-sm font-medium ${
                  isActive
                    ? 'text-gyaan-gold'
                    : 'text-white hover:text-gyaan-gold'
                }`
              }
            >
              Contact Us
            </NavLink>
            {/* <NavLink
              to="/courses"
              className={({isActive}) =>
                `text-sm font-medium ${isActive ? 'text-gyaan-gold' : 'text-white hover:text-gyaan-gold'}`
              }
            >
              Courses
            </NavLink> */}
            <div className="border-l border-white/30 h-6 mx-2"></div>
            <NavLink to="/register">
              <Button variant="gold">Register</Button>
            </NavLink>
            <NavLink to="/login">
              <Button
                variant="outline"
                glow="gold"
                className="border-white text-black hover:bg-black/10 hover:text-white"
              >
                Login
              </Button>
            </NavLink>
          </nav>

          {/* Mobile menu button */}
          <button className="md:hidden text-white" onClick={toggleMenu}>
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden bg-gyaan-navy border-t border-white/20 animate-fade-in">
          <div className="container mx-auto px-4 py-3 flex flex-col space-y-4">
            <NavLink
              to="/"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md ${
                  isActive
                    ? 'bg-white/20 text-gyaan-gold font-medium'
                    : 'text-white hover:bg-white/10'
                }`
              }
              onClick={closeMenu}
            >
              Home
            </NavLink>
            <NavLink
              to="/about"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md ${
                  isActive
                    ? 'bg-white/20 text-gyaan-gold font-medium'
                    : 'text-white hover:bg-white/10'
                }`
              }
              onClick={closeMenu}
            >
              About RRU
            </NavLink>
            <NavLink
              to="/news"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md ${
                  isActive
                    ? 'bg-white/20 text-gyaan-gold font-medium'
                    : 'text-white hover:bg-white/10'
                }`
              }
              onClick={closeMenu}
            >
              News & Announcements
            </NavLink>
            <NavLink
              to="/contact"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md ${
                  isActive
                    ? 'bg-white/20 text-gyaan-gold font-medium'
                    : 'text-white hover:bg-white/10'
                }`
              }
              onClick={closeMenu}
            >
              Contact Us
            </NavLink>
            <NavLink
              to="/courses"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md ${
                  isActive
                    ? 'bg-white/20 text-gyaan-gold font-medium'
                    : 'text-white hover:bg-white/10'
                }`
              }
              onClick={closeMenu}
            >
              Courses
            </NavLink>
            <div className="border-t border-white/20 pt-4 flex flex-col space-y-3">
              <NavLink
                to="/register"
                className="w-full bg-gyaan-gold text-white py-2 px-4 rounded-md text-center hover:bg-gyaan-gold/90"
                onClick={closeMenu}
              >
                Register
              </NavLink>
              <NavLink
                to="/login"
                className="w-full border border-black text-black py-2 px-4 rounded-md text-center hover:bg-white/10"
                onClick={closeMenu}
              >
                Login
              </NavLink>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
