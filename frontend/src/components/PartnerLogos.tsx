import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, Building } from 'lucide-react';
import { useApi } from '@/hooks/use-api';
import { partnersAPI } from '@/services/api';
import { useAccessibility } from '@/providers/AccessibilityProvider';
import useEmblaCarousel from 'embla-carousel-react';
// Import Autoplay plugin
import { cn } from '@/lib/utils';

type PartnerInfo = {
  id: number;
  name: string;
  logo: string;
  description: string;
};

// Fallback data for partners when API fails
const fallbackPartners: PartnerInfo[] = [
  {
    id: 1,
    name: 'Delhi Police',
    logo: '/src/assets/images/Delhi_Police_Logo.png',
    description:
      'Delhi Police is the law enforcement agency for the National Capital Territory of Delhi, providing security and maintaining public order.',
  },
  {
    id: 2,
    name: 'Arunachal Pradesh Police',
    logo: '/src/assets/images/Arunachal_Pradesh_Police_Logo.png',
    description:
      'Arunachal Pradesh Police is responsible for maintaining law and order in the state of Arunachal Pradesh.',
  },
  {
    id: 3,
    name: 'Assam Police',
    logo: '/src/assets/images/Assam_Police_badge.png',
    description:
      'Assam Police is the law enforcement agency for the state of Assam, providing security and maintaining public order.',
  },
  {
    id: 4,
    name: 'Chandigarh Police',
    logo: '/src/assets/images/Chandigarh_Police_Logo.png',
    description:
      'Chandigarh Police is responsible for law enforcement in the Union Territory of Chandigarh.',
  },
  {
    id: 5,
    name: 'Chhattisgarh Police',
    logo: '/src/assets/images/Chhattisgarh_Police_Insignia.png',
    description:
      'Chhattisgarh Police is the law enforcement agency for the state of Chhattisgarh.',
  },
  {
    id: 6,
    name: 'Dadra & Nagar Haveli and Daman & Diu Police',
    logo: '/src/assets/images/Dadra & Nagra Haveli and Daman and Diu Police.jpg',
    description:
      'Police force responsible for maintaining law and order in the Union Territory of Dadra & Nagar Haveli and Daman & Diu.',
  },
  {
    id: 7,
    name: 'Goa Police',
    logo: '/src/assets/images/Emblem_of_Goa_Police.png',
    description:
      'Goa Police is the law enforcement agency for the state of Goa.',
  },
  {
    id: 8,
    name: 'Haryana Police',
    logo: '/src/assets/images/Haryana-police-logo.png',
    description:
      'Haryana Police is responsible for maintaining law and order in the state of Haryana.',
  },
  {
    id: 9,
    name: 'Himachal Pradesh Police',
    logo: '/src/assets/images/Himachal_Pradesh_Police_Logo.png',
    description:
      'Himachal Pradesh Police is the law enforcement agency for the state of Himachal Pradesh.',
  },
  {
    id: 10,
    name: 'Lakshadweep Police',
    logo: '/src/assets/images/Lakshadweep_Police_Logo.png',
    description:
      'Lakshadweep Police is responsible for maintaining law and order in the Union Territory of Lakshadweep.',
  },
  {
    id: 11,
    name: 'Maharashtra Police',
    logo: '/src/assets/images/Maharashtra_Police_Logo.png',
    description:
      'Maharashtra Police is the law enforcement agency for the state of Maharashtra, providing security and maintaining public order.',
  },
  {
    id: 12,
    name: 'Meghalaya Police',
    logo: '/src/assets/images/Meghalaya_Police_Logo.png',
    description:
      'Meghalaya Police is responsible for maintaining law and order in the state of Meghalaya.',
  },
  {
    id: 13,
    name: 'Puducherry Police',
    logo: '/src/assets/images/Puducherry_Police_logo.png',
    description:
      'Puducherry Police is the law enforcement agency for the Union Territory of Puducherry.',
  },
  {
    id: 14,
    name: 'Punjab Police',
    logo: '/src/assets/images/Logo_of_Punjab_Police_(India).webp',
    description:
      'Punjab Police is responsible for maintaining law and order in the state of Punjab.',
  },
  {
    id: 15,
    name: 'Uttarakhand Police',
    logo: '/src/assets/images/Uttarakhand_Police_Logo.png',
    description:
      'Uttarakhand Police is the law enforcement agency for the state of Uttarakhand.',
  },
  {
    id: 16,
    name: 'Uttar Pradesh Police',
    logo: '/src/assets/images/Uttar_Pradesh_Logo.png',
    description:
      'Uttar Pradesh Police is responsible for maintaining law and order in the state of Uttar Pradesh.',
  },
  {
    id: 17,
    name: 'Andaman & Nicobar Police',
    logo: '/src/assets/images/ANP-logo_new.jpg',
    description:
      'Andaman & Nicobar Police is the law enforcement agency for the Union Territory of Andaman & Nicobar Islands.',
  },
];

// Empty state component for when no data is available
const EmptyState = () => (
  <div className="col-span-full py-8 text-center">
    <div className="flex flex-col items-center justify-center">
      <Building className="h-12 w-12 text-gray-300 mb-4" />
      <h3 className="text-lg font-medium text-gray-500 mb-1">
        No Partners Available
      </h3>
      <p className="text-gray-400 max-w-md mx-auto">
        Partner information is currently unavailable. Please check back later.
      </p>
    </div>
  </div>
);

// Custom hook for auto-scrolling carousel
const useAutoScrollCarousel = (options = {}) => {
  const { reducedMotion } = useAccessibility();
  const [viewportRef, emblaApi] = useEmblaCarousel({
    loop: true, // Enable infinite looping
    align: 'start', // Align slides to the start
    skipSnaps: false, // Don't skip snap positions
    dragFree: true, // Allow free-form dragging
    containScroll: 'trimSnaps', // Prevent overscrolling
    direction: 'ltr', // Left-to-right scrolling direction
    slidesToScroll: 1, // Scroll one slide at a time
  });

  const [isPaused, setIsPaused] = useState(false);
  const autoplayTimerRef = useRef<number | null>(null);
  const autoplayDelay = 2000; // Time between auto-scrolls in milliseconds

  // Function to start autoplay
  const startAutoplay = useCallback(() => {
    if (reducedMotion || !emblaApi) return;

    stopAutoplay(); // Clear any existing timer

    autoplayTimerRef.current = window.setInterval(() => {
      if (emblaApi && !isPaused) {
        emblaApi.scrollNext();
      }
    }, autoplayDelay);
  }, [emblaApi, isPaused, reducedMotion]);

  // Function to stop autoplay
  const stopAutoplay = useCallback(() => {
    if (autoplayTimerRef.current !== null) {
      window.clearInterval(autoplayTimerRef.current);
      autoplayTimerRef.current = null;
    }
  }, []);

  // Initialize autoplay when emblaApi is ready
  useEffect(() => {
    if (emblaApi && !reducedMotion) {
      startAutoplay();
    }

    return () => {
      stopAutoplay();
    };
  }, [emblaApi, startAutoplay, stopAutoplay, reducedMotion]);

  // Handle pause/resume based on isPaused state
  useEffect(() => {
    if (isPaused) {
      stopAutoplay();
    } else if (emblaApi && !reducedMotion) {
      startAutoplay();
    }
  }, [isPaused, emblaApi, reducedMotion, startAutoplay, stopAutoplay]);

  // Handle mouse enter to pause scrolling
  const onMouseEnter = useCallback(() => {
    if (!reducedMotion) {
      setIsPaused(true);
    }
  }, [reducedMotion]);

  // Handle mouse leave to resume scrolling
  const onMouseLeave = useCallback(() => {
    if (!reducedMotion) {
      setIsPaused(false);
    }
  }, [reducedMotion]);

  // Handle focus events (for keyboard users)
  const onFocus = useCallback(() => {
    onMouseEnter();
  }, [onMouseEnter]);

  const onBlur = useCallback(() => {
    onMouseLeave();
  }, [onMouseLeave]);

  return {
    viewportRef,
    emblaApi,
    isPaused,
    onMouseEnter,
    onMouseLeave,
    onFocus,
    onBlur,
  };
};

// Loading state component for carousel
const LoadingState = () => (
  <div className="flex space-x-4 px-4">
    {[...Array(6)].map((_, index) => (
      <div
        key={index}
        className="flex-none w-40 bg-white p-4 rounded-lg shadow-md"
      >
        <div className="flex flex-col items-center">
          <Skeleton className="h-20 w-20 rounded-full mb-4" />
          <Skeleton className="h-4 w-32 mb-2" />
        </div>
      </div>
    ))}
  </div>
);

const PartnerLogos = () => {
  const { reducedMotion } = useAccessibility();
  const [selectedPartner, setSelectedPartner] = useState<PartnerInfo | null>(
    null
  );
  const [usingFallback, setUsingFallback] = useState(false);

  // Setup carousel with auto-scrolling
  const {
    viewportRef,
    emblaApi,
    isPaused,
    onMouseEnter,
    onMouseLeave,
    onFocus,
    onBlur,
  } = useAutoScrollCarousel();

  // Fetch partners data from API
  const {
    data: apiPartners,
    isLoading,
    error,
  } = useApi<PartnerInfo[]>(() => partnersAPI.getPartners(), {
    onError: (error) => {
      console.error('Failed to load partners data:', error);
      setUsingFallback(true);
    },
  });

  // Use API data if available, otherwise use fallback data
  const partners =
    usingFallback || error || !apiPartners ? fallbackPartners : apiPartners;

  const openPartnerInfo = (partner: PartnerInfo) => {
    setSelectedPartner(partner);
  };

  const closePartnerInfo = () => {
    setSelectedPartner(null);
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gyaan-navy mb-4">
            Our MoU Partners
          </h2>
          <div className="w-24 h-1 bg-gyaan-gold mx-auto mb-6"></div>
          <p className="text-center text-gray-600 max-w-3xl mx-auto">
            We collaborate to work towards accrediting training programmes,
            undertaking training programmes for up-skilling, re-skilling
            security personnels and encouraging innovations in the fields of
            safety and security.
          </p>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-lg">
          <div
            className="overflow-hidden rounded-lg"
            ref={viewportRef}
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
            aria-label="Our MoU Partners"
            role="region"
            tabIndex={-1}
          >
            {isLoading && !usingFallback ? (
              <LoadingState />
            ) : partners.length === 0 ? (
              <EmptyState />
            ) : (
              <div className="flex py-2">
                {partners.map((partner) => (
                  <div
                    key={partner.id}
                    className={cn(
                      'partner-card flex-none mx-3 my-4 w-32 sm:w-40 p-3 sm:p-4 cursor-pointer',
                      'flex items-center justify-center',
                      !reducedMotion &&
                        'hover:shadow-lg transition-all duration-300',
                      !reducedMotion && 'hover:-translate-y-2',
                      'focus-visible:ring-2 focus-visible:ring-gyaan-gold focus-visible:outline-none'
                    )}
                    onClick={() => openPartnerInfo(partner)}
                    role="button"
                    aria-label={`View details for ${partner.name}`}
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault(); // Prevent scrolling on space
                        openPartnerInfo(partner);
                      }
                    }}
                    onFocus={onFocus}
                    onBlur={onBlur}
                  >
                    <div className="text-center">
                      <div className="h-16 sm:h-20 flex items-center justify-center">
                        {partner.logo ? (
                          <img
                            src={partner.logo}
                            alt={`${partner.name} Logo`}
                            className="w-16 h-16 sm:w-20 sm:h-20 object-contain"
                            loading="lazy"
                          />
                        ) : (
                          <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gray-200 rounded-full flex items-center justify-center text-gray-500 text-xl">
                            {partner.name.charAt(0)}
                          </div>
                        )}
                      </div>
                      <p className="mt-2 text-xs md:text-sm text-center font-bold text-gyaan-navy line-clamp-2 h-8 sm:h-10">
                        {partner.name}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {partners && partners.length > 0 && (
          <div className="text-center mt-8">
            <p className="text-gray-600 font-medium">
              And many more organizations committed to excellence in security
              training.
            </p>
          </div>
        )}
      </div>

      <Dialog open={!!selectedPartner} onOpenChange={closePartnerInfo}>
        <DialogContent className="border border-gray-200">
          <DialogHeader>
            <DialogTitle className="text-xl text-gyaan-navy font-bold border-b border-gray-200 pb-2">
              {selectedPartner?.name}
            </DialogTitle>
            <DialogDescription className="pt-4">
              <div className="flex flex-col items-center mb-6">
                {selectedPartner?.logo ? (
                  <div className="bg-white p-4 rounded-full shadow-lg mb-4 border border-gray-200">
                    <img
                      src={selectedPartner.logo}
                      alt={`${selectedPartner.name} Logo`}
                      className="w-32 h-32 object-contain"
                      loading="lazy"
                    />
                  </div>
                ) : (
                  <div className="w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center text-gyaan-navy text-3xl font-bold mb-4 border border-gray-200 shadow-lg">
                    {selectedPartner?.name.charAt(0)}
                  </div>
                )}
              </div>
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <p className="text-gray-700 leading-relaxed">
                  {selectedPartner?.description}
                </p>
              </div>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </section>
  );
};

export default PartnerLogos;
